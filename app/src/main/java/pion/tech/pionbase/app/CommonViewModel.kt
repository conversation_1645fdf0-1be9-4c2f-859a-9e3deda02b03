package pion.tech.pionbase.app

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collect
import pion.tech.pionbase.base.BaseViewModel
import pion.tech.pionbase.base.launchIO
import pion.tech.pionbase.data.model.appCategory.AppCategoryUIModel
import pion.tech.pionbase.data.model.appCategory.toPresentation
import pion.tech.pionbase.data.model.font.FontUIModel
import pion.tech.pionbase.data.model.font.toPresentation
import pion.tech.pionbase.data.model.remoteConfig.RemoteConfigDtoModel
import pion.tech.pionbase.data.model.template.TemplateUIModel
import pion.tech.pionbase.data.model.template.toPresentation
import pion.tech.pionbase.data.repository.apiRepository.ApiRepository
import pion.tech.pionbase.data.repository.dataStore.DataStoreRepository
import pion.tech.pionbase.data.repository.remoteConfig.RemoteConfigRepository
import pion.tech.pionbase.util.UiState
import pion.tech.pionbase.util.handleApiCall
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class CommonViewModel
@Inject
constructor(
    private val remoteConfigRepository: RemoteConfigRepository,
    private val apiRepository: ApiRepository,
    private val dataStoreRepository: DataStoreRepository,
) : BaseViewModel() {
    private val _remoteConfigUiState =
        MutableStateFlow<UiState<RemoteConfigDtoModel>>(UiState.None)
    val remoteConfigUiState = _remoteConfigUiState.asStateFlow()

    private fun fetchRemoteConfig() {
        handleApiCall(
            stateFlow = _remoteConfigUiState,
            apiCall = { remoteConfigRepository.fetchRemoteConfig() },
            skipIfInProgress = false,
        )
    }

    private val _getCategoryUiState =
        MutableStateFlow<UiState<List<AppCategoryUIModel>>>(UiState.None)
    val getCategoryUiState = _getCategoryUiState.asStateFlow()

    private val _getTemplateUiState =
        MutableStateFlow<UiState<List<TemplateUIModel>>>(UiState.None)
    val getTemplateUiState = _getTemplateUiState.asStateFlow()

    private val _getFontUiState =
        MutableStateFlow<UiState<List<FontUIModel>>>(UiState.None)
    val getFontUiState = _getFontUiState.asStateFlow()

    fun getAppId() {
        handleApiCall(
            stateFlow = _getCategoryUiState,
            apiCall = { apiRepository.getAppCategory().also { Timber.d("getAppId: Calling $it") } },
            transform = { data ->
                data.map { item -> item.toPresentation() }.also { Timber.d("getAppId: $it") }
            },
            skipIfInProgress = false,

            )
    }

    fun getTemplate(categoryId: String) {
        handleApiCall(
            stateFlow = _getTemplateUiState,
            apiCall = {
                apiRepository.getTemplateData(categoryId)
                    .also { Timber.d("getTemplate: Calling $it") }
            },
            transform = { data ->
                data.map { item -> item.toPresentation() }.also { Timber.d("getTemplate: $it") }
            },
        )
    }

    fun getFont(categoryId: String) {
        handleApiCall(
            stateFlow = _getFontUiState,
            apiCall = {
                apiRepository.getAllFonts(categoryId).also { Timber.d("getFont: Calling $it") }
            },
            transform = { data ->
                data.map { item -> item.toPresentation() }.also { Timber.d("getFont: $it") }
            },
        )
    }

    fun setPremium(isPremium: Boolean) {
        launchIO {
            dataStoreRepository.setIsPremium(isPremium).collect()
        }
    }

    private val _checkGdprState = MutableStateFlow(GDPRState.NONE)
    val checkGdprState = _checkGdprState.asStateFlow()

    fun setGdprState(state: GDPRState) {
        _checkGdprState.value = state
    }

    init {
        fetchRemoteConfig()
        getAppId()
    }
}

enum class GDPRState {
    NONE,
    LOADING,
    DONE,
}
