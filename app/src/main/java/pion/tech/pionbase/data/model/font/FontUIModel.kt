package pion.tech.pionbase.data.model.font

import android.content.Context
import android.graphics.drawable.Drawable
import android.os.Parcelable
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.parcelize.Parcelize
import pion.tech.pionbase.util.FontUtils
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

@Parcelize
data class FontUIModel(
    val name: String,
    val fontLight: String? = null,
    val fontRegular: String? = null,
    val fontMedium: String? = null,
    val fontSemiBold: String? = null,
    val fontBold: String? = null,
) : Parcelable {
    companion object {
        fun createDefaultFont() =
            FontUIModel(
                name = "Default",
            )
    }
}

@Parcelize
data class FontUIModelSelector(
    val fontUIModel: FontUIModel,
    val isSelected: Boolean = false,
) : Parcelable

fun FontDtoModel.toPresentation() =
    FontUIModel(
        name = this.name,
        fontLight = this.customFields.fontLight,
        fontRegular = this.customFields.fontRegular,
        fontMedium = this.customFields.fontMedium,
        fontSemiBold = this.customFields.fontSemiBold,
        fontBold = this.customFields.fontBold,
    )

fun List<FontUIModel>.preloadPreviewFont(
    context: Context,
    coroutineScope: CoroutineScope,
    timeOut: Long = 15000L,
    onSuccess: () -> Unit = {},
    onFailure: () -> Unit = {},
) {
    try {
        // List of fonts to preload
        val fontsToLoad =
            listOfNotNull(
                <EMAIL> {
                    listOfNotNull(
                        it.fontLight,
                        it.fontRegular,
                        it.fontMedium,
                        it.fontSemiBold,
                        it.fontBold,
                    )
                }
            ).flatten().filter { it.isNotEmpty() }.flatten()

        // Total items to preload (images + fonts)
        val totalItems = fontsToLoad.size

        // If no items to load, call onSuccess and return
        if (totalItems == 0) {
            coroutineScope.launch(Dispatchers.Main) {
                onSuccess()
            }
            return
        }

        val completedCount = AtomicInteger(0)
        val isCompleted = AtomicBoolean(false)

        // 15 second timeout to auto call onSuccess
        val timeoutJob: Job =
            coroutineScope.launch(Dispatchers.Main) {
                delay(timeOut)
                if (isCompleted.compareAndSet(false, true)) {
                    withContext(Dispatchers.Main) {
                        Timber.d("Auto timeout after 15 seconds, calling onSuccess")
                        onSuccess()
                    }
                }
            }

        // Helper function to check completion
        fun checkCompletion(typeComplete: TypeLoadThemeComplete) {
            val currentCount = completedCount.incrementAndGet()
            Timber.d("typeComplete $typeComplete")
            Timber.d("totalItems $totalItems")
            Timber.d("completedCount $currentCount")

            if (currentCount >= totalItems && isCompleted.compareAndSet(false, true)) {
                timeoutJob.cancel() // Cancel timeout job
                coroutineScope.launch(Dispatchers.Main) {
                    onSuccess()
                }
            }
        }


        // Preload fonts
        fontsToLoad.forEach { fontUrl ->
            coroutineScope.launch(Dispatchers.IO) {
                try {
                    FontUtils.loadFontFromUrl(context, fontUrl)
                    checkCompletion(typeComplete = TypeLoadThemeComplete.FONT)
                } catch (e: Exception) {
                    // Font loading failed, but still count as completed
                    checkCompletion(typeComplete = TypeLoadThemeComplete.FONT)
                }
            }
        }
    } catch (e: Exception) {
        coroutineScope.launch(Dispatchers.Main) {
            onFailure.invoke()
        }
    }
}

enum class TypeLoadThemeComplete {
    IMAGE,
    FONT,
}
