package pion.tech.pionbase.data.model.font

import com.google.gson.annotations.SerializedName

data class FontDtoModel(
    @SerializedName("name")
    val name: String,
    @SerializedName("custom_fields")
    val customFields: FontCustomFieldDtoModel,
)

data class FontCustomFieldDtoModel(
    @SerializedName("fontLight")
    val fontLight: String? = null,
    @SerializedName("fontRegular")
    val fontRegular: String? = null,
    @SerializedName("fontMedium")
    val fontMedium: String? = null,
    @SerializedName("fontSemiBold")
    val fontSemiBold: String? = null,
    @SerializedName("fontBold")
    val fontBold: String? = null,
)
