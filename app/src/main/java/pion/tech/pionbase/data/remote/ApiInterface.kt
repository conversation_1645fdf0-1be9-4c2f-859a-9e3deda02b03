package pion.tech.pionbase.data.remote

import pion.tech.pionbase.data.model.ApiObjectResponseData
import pion.tech.pionbase.data.model.appCategory.AppCategoryDtoModel
import pion.tech.pionbase.data.model.font.FontDtoModel
import pion.tech.pionbase.data.model.template.TemplateResponseDtoModel
import retrofit2.http.GET
import retrofit2.http.Query

interface ApiInterface {
    @GET("api/v5.0/public/categories?app_id=23d1f959-6c8c-431d-bd78-0ce87ad1985b")
    suspend fun getAppCategory(): ApiObjectResponseData<List<AppCategoryDtoModel>>

    @GET("api/v5.0/public/items/get-all?region_code=%7Bregion_code%7D")
    suspend fun getAllTemplate(
        @Query("category_id") categoryId: String,
    ): ApiObjectResponseData<List<TemplateResponseDtoModel>>

    @GET("api/v5.0/public/items/get-all?region_code=%7Bregion_code%7D")
    suspend fun getAllFonts(
        @Query("category_id") categoryId: String,
    ): ApiObjectResponseData<List<FontDtoModel>>
}
