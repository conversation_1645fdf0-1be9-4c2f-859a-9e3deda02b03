package pion.tech.pionbase.feature.create_banner.tabs.tab_text.adapter

import android.graphics.Typeface
import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import pion.tech.pionbase.R
import pion.tech.pionbase.base.BaseListAdapter
import pion.tech.pionbase.base.createDiffCallback
import pion.tech.pionbase.data.model.font.FontUIModelSelector
import pion.tech.pionbase.databinding.ItemFontSelectionBinding
import pion.tech.pionbase.util.FontUtils
import pion.tech.pionbase.util.setPreventDoubleClick

class FontAdapter(
    private val viewLifecycleOwner: LifecycleOwner,
    private val onFontSelected: (FontUIModelSelector) -> Unit
) : BaseListAdapter<FontUIModelSelector, ItemFontSelectionBinding>(
    createDiffCallback(
        areItemsTheSame = { oldItem, newItem -> oldItem.fontUIModel == newItem.fontUIModel },
        areContentsTheSame = { oldItem, newItem -> oldItem == newItem },
    )
) {

    override fun getLayoutRes(viewType: Int): Int {
        return R.layout.item_font_selection
    }

    override fun bindView(
        binding: ItemFontSelectionBinding,
        item: FontUIModelSelector,
        position: Int
    ) {
        binding.tvFontName.text = item.fontUIModel.name

        viewLifecycleOwner.lifecycleScope.launch {
            FontUtils.loadAndApplyFont(
                context = binding.root.context,
                fontUrl = item.fontUIModel.fontRegular,
                textView = binding.tvFontName,
            )
        }

        //binding.tvFontName.typeface = Typeface.createFromAsset(binding.root.context.assets, item.fontUIModel.fontRegular ?: "")
        binding.tvFontName.isSelected = true

        // Show/hide selection overlay
        if (item.isSelected) {
            binding.ivSelectionOverlay.setBackgroundResource(R.drawable.ic_background_font_selected)
        } else {
            binding.ivSelectionOverlay.setBackgroundResource(R.drawable.ic_background_font_unselected)
        }

        binding.root.setPreventDoubleClick {
            onFontSelected(item)
        }
    }

}
