package pion.tech.pionbase.feature.create_banner.tabs.tab_text.bottomsheet

import android.os.Bundle
import androidx.recyclerview.widget.GridLayoutManager
import dagger.hilt.android.AndroidEntryPoint
import pion.tech.pionbase.R
import pion.tech.pionbase.base.BaseBottomSheetDialogFragment
import pion.tech.pionbase.data.model.font.FontUIModel
import pion.tech.pionbase.data.model.font.FontUIModelSelector
import pion.tech.pionbase.databinding.BottomSheetFontSelectionBinding
import pion.tech.pionbase.feature.create_banner.tabs.tab_text.adapter.FontAdapter
import pion.tech.pionbase.util.parcelableArrayList
import pion.tech.pionbase.util.setPreventDoubleClick

@AndroidEntryPoint
class FontSelectionBottomSheet : BaseBottomSheetDialogFragment<BottomSheetFontSelectionBinding>(
    R.layout.bottom_sheet_font_selection
) {

    private var onFontSelectedListener: ((FontUIModel?) -> Unit)? = null
    private var fonts: List<FontUIModelSelector> = emptyList()

    private val fontAdapter by lazy {
        FontAdapter(viewLifecycleOwner) { fontUIModelSelected ->
            onFontSelected(fontUIModelSelected)
        }
    }

    private fun onFontSelected(fontItem: FontUIModelSelector) {
        val newFontsList = fonts.map { it.copy(isSelected = it.fontUIModel.name == fontItem.fontUIModel.name) }
        fonts = newFontsList
        fontAdapter.submitList(fonts)
    }

    companion object {
        private const val ARG_FONTS = "fonts"
        fun newInstance(
            fonts: List<FontUIModelSelector>,
        ): FontSelectionBottomSheet {
            return FontSelectionBottomSheet().apply {
                arguments = Bundle().apply {
                    putParcelableArrayList(ARG_FONTS, ArrayList(fonts))
                }
            }
        }
    }

    fun setOnFontSelectedListener(listener: (FontUIModel?) -> Unit) {
        onFontSelectedListener = listener
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        arguments?.let { args ->
            fonts = args.parcelableArrayList(ARG_FONTS) ?: emptyList()
        }
        setupRecyclerView()
        setupFontList()
    }

    private fun setupRecyclerView() {
        binding.rvFonts.apply {
            layoutManager = GridLayoutManager(requireContext(), 2)
            adapter = fontAdapter
        }
    }

    private fun setupFontList() {
        fontAdapter.submitList(fonts)
    }

    override fun addEvent(savedInstanceState: Bundle?) {
        super.addEvent(savedInstanceState)
        
        binding.btnClose.setPreventDoubleClick {
            dismiss()
        }
        
        binding.btnApply.setPreventDoubleClick {
            onFontSelectedListener?.invoke(fonts.firstOrNull { it.isSelected }?.fontUIModel)
            dismiss()
        }
    }
}
