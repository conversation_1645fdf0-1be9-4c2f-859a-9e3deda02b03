package pion.tech.pionbase.feature.create_banner.tabs.tab_text

import android.view.View
import dagger.hilt.android.AndroidEntryPoint
import pion.tech.pionbase.base.BaseFragment
import pion.tech.pionbase.databinding.FragmentCreateBannerTabTextBinding
import pion.tech.pionbase.util.FontUtils
import pion.tech.pionbase.util.collectFlowOnView

@AndroidEntryPoint
class CreateBannerTabTextFragment : BaseFragment<FragmentCreateBannerTabTextBinding, CreateBannerTabTextViewModel>(
    FragmentCreateBannerTabTextBinding::inflate,
    CreateBannerTabTextViewModel::class.java,
) {

    override fun init(view: View) {
        setupTextSizeSeekBar()
        setupOutlineSeekBar()
        setupShadowSeekBar()
        setUpTextColorPicker()
        setUpOutlineColorPicker()
        setUpShadowColorPicker()
        setupButtonClearFormat()
        setupButtonBold()
        setupButtonItalic()
        setupButtonUnderline()
        setupFontSelection()
    }

    override fun subscribeObserver(view: View) {
        observeTextStyleData()

        createBannerCommonViewModel.selectedFontUiModel.collectFlowOnView(viewLifecycleOwner) {
            binding.tvSelectedFont.text = it.name
            FontUtils.loadAndApplyFont(
                context = requireContext(),
                fontUrl = it.fontRegular,
                textView = binding.tvSelectedFont,
            )
        }
    }
}