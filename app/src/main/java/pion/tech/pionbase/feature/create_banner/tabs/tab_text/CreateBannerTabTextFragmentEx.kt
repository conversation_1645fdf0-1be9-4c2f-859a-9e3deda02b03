package pion.tech.pionbase.feature.create_banner.tabs.tab_text

import android.graphics.Color
import android.widget.ImageView
import android.widget.SeekBar
import androidx.core.graphics.toColorInt
import androidx.lifecycle.lifecycleScope
import pion.tech.pionbase.R
import pion.tech.pionbase.data.model.create_banner.ColorPickerType
import pion.tech.pionbase.data.model.create_banner.ColorSelectedData
import pion.tech.pionbase.data.model.create_banner.TextStyleData
import pion.tech.pionbase.data.model.font.FontUIModel
import pion.tech.pionbase.data.model.font.FontUIModelSelector
import pion.tech.pionbase.data.model.font.preloadPreviewFont
import pion.tech.pionbase.feature.create_banner.CreateBannerCommonViewModel
import pion.tech.pionbase.feature.create_banner.tabs.tab_text.bottomsheet.ColorPickerBottomSheet
import pion.tech.pionbase.feature.create_banner.tabs.tab_text.bottomsheet.FontSelectionBottomSheet
import pion.tech.pionbase.util.UiState
import pion.tech.pionbase.util.collectFlowOnView
import pion.tech.pionbase.util.displayToast
import pion.tech.pionbase.util.setPreventDoubleClick
import pion.tech.pionbase.util.setTintColor
import timber.log.Timber

fun CreateBannerTabTextFragment.setupTextSizeSeekBar() {
    binding.seekBarTextSize.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            if (fromUser) {
                val textSize = CreateBannerCommonViewModel.MIN_TEXT_SIZE +
                    (progress * (CreateBannerCommonViewModel.MAX_TEXT_SIZE - CreateBannerCommonViewModel.MIN_TEXT_SIZE)) / 100
                createBannerCommonViewModel.updateTextSize(textSize)
            }
        }

        override fun onStartTrackingTouch(seekBar: SeekBar?) {}
        override fun onStopTrackingTouch(seekBar: SeekBar?) {}
    })
}

fun CreateBannerTabTextFragment.setupOutlineSeekBar() {
    binding.seekBarOutline.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            if (fromUser) {
                createBannerCommonViewModel.updateOutlineWidth(progress)
            }
        }

        override fun onStartTrackingTouch(seekBar: SeekBar?) {}
        override fun onStopTrackingTouch(seekBar: SeekBar?) {}
    })
}

fun CreateBannerTabTextFragment.setupShadowSeekBar() {
    binding.seekBarShadow.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            if (fromUser) {
                createBannerCommonViewModel.updateShadowRadius(progress)
            }
        }

        override fun onStartTrackingTouch(seekBar: SeekBar?) {}
        override fun onStopTrackingTouch(seekBar: SeekBar?) {}
    })
}

fun CreateBannerTabTextFragment.showColorPickerBottomSheet(
    currentColor: ColorSelectedData,
    title: String = "",
    supportGradient: Boolean = false,
    onColorSelected: (ColorSelectedData) -> Unit
) {
    val bottomSheet = ColorPickerBottomSheet.newInstance(currentColor, title, supportGradient)
    bottomSheet.setOnColorSelectedListener(onColorSelected)
    bottomSheet.show(parentFragmentManager)
}


fun CreateBannerTabTextFragment.updateStyleTextImageState(
    imageView: ImageView,
    isSelected: Boolean
) {
    imageView.setTintColor(
        if (isSelected) {
            R.color.blue_1b71f4
        } else {
            R.color.gray_85888e
        }
    )
}

fun CreateBannerTabTextFragment.setUpTextColorPicker() {
    binding.viewTextColorPicker.setPreventDoubleClick {
        onTextColorPickerClick()
    }
}

fun CreateBannerTabTextFragment.setUpOutlineColorPicker() {
    binding.viewOutlineColorPicker.setPreventDoubleClick {
        onOutlineColorPickerClick()
    }
}

fun CreateBannerTabTextFragment.setUpShadowColorPicker() {
    binding.viewShadowColorPicker.setPreventDoubleClick {
        onShadowColorPickerClick()
    }
}

fun CreateBannerTabTextFragment.setupButtonClearFormat() {
    binding.btnClearFormat.setPreventDoubleClick {
        onClearFormatClick()
    }
}

fun CreateBannerTabTextFragment.setupButtonBold() {
    binding.btnBold.setPreventDoubleClick {
        onBoldClick()
    }
}

fun CreateBannerTabTextFragment.setupButtonItalic() {
    binding.btnItalic.setPreventDoubleClick {
        onItalicClick()
    }
}

fun CreateBannerTabTextFragment.setupButtonUnderline() {
    binding.btnUnderline.setPreventDoubleClick {
        onUnderlineClick()
    }
}

fun CreateBannerTabTextFragment.setupFontSelection() {
    binding.tvSelectedFont.setPreventDoubleClick {
        onFontSelectionClick()
    }
}

fun CreateBannerTabTextFragment.observeTextStyleData() {
    createBannerCommonViewModel.textStyleData.collectFlowOnView(viewLifecycleOwner) {
        updateUIFromTextStyleData(it)
    }

    /*createBannerCommonViewModel.selectedFontIndex.collectFlowOnView(viewLifecycleOwner) { fontIndex ->
        updateSelectedFontDisplay(fontIndex)
    }*/
}

fun CreateBannerTabTextFragment.updateUIFromTextStyleData(data: TextStyleData) {
    // Update seekbars
    val textSizeProgress = ((data.textSize - CreateBannerCommonViewModel.MIN_TEXT_SIZE) * 100) /
            (CreateBannerCommonViewModel.MAX_TEXT_SIZE - CreateBannerCommonViewModel.MIN_TEXT_SIZE)
    binding.seekBarTextSize.progress = textSizeProgress
    binding.seekBarOutline.progress = data.outlineWidth
    binding.seekBarShadow.progress = data.shadowRadius

    // Update color pickers
    if (data.textColor.colorPickerType == ColorPickerType.SOLID) {
        binding.viewTextColorPicker.setSolidColor(data.textColor.solidColor ?: Color.WHITE)
    } else {
        binding.viewTextColorPicker.setLinearGradient(
            data.textColor.startGradientColor ?: "#FFFFFFFF".toColorInt(),
            data.textColor.endGradientColor ?: "#FF000000".toColorInt(),
            data.textColor.angleGradient ?: 0f
        )
    }

    binding.viewOutlineColorPicker.solidColor = data.outlineColor.toColorInt()
    binding.viewShadowColorPicker.solidColor = data.shadowColor.toColorInt()

    // Update style buttons
    updateStyleTextImageState(binding.btnBold, data.isBold)
    updateStyleTextImageState(binding.btnItalic, data.isItalic)
    updateStyleTextImageState(binding.btnUnderline, data.isUnderline)

    // Update font display
    //binding.tvSelectedFont.text = data.fontFamily
}

/*fun CreateBannerTabTextFragment.updateSelectedFontDisplay(fontIndex: Int) {
    val fonts = createBannerCommonViewModel.availableFonts.value
    if (fontIndex < fonts.size) {
        binding.tvSelectedFont.text = fonts[fontIndex].displayName
    }
}*/

fun CreateBannerTabTextFragment.onTextColorPickerClick() {
    val currentColor = createBannerCommonViewModel.textStyleData.value.textColor
    showColorPickerBottomSheet(
        currentColor = currentColor,
        title = getString(R.string.text_color),
        supportGradient = true,
    ) { selectedColor ->
        createBannerCommonViewModel.updateTextColor(selectedColor)
    }
}

fun CreateBannerTabTextFragment.onOutlineColorPickerClick() {
    val currentColor = createBannerCommonViewModel.textStyleData.value.outlineColor
    showColorPickerBottomSheet(
        currentColor = ColorSelectedData(
            colorPickerType = ColorPickerType.SOLID,
            solidColor = currentColor.toColorInt()
        ),
        title = getString(R.string.outline_color),
        supportGradient = false,
    ) { selectedColor ->
        createBannerCommonViewModel.updateOutlineColor(String.format("#%08X", selectedColor.solidColor))
    }
}

fun CreateBannerTabTextFragment.onShadowColorPickerClick() {
    val currentColor = createBannerCommonViewModel.textStyleData.value.shadowColor
    showColorPickerBottomSheet(
        currentColor = ColorSelectedData(
            colorPickerType = ColorPickerType.SOLID,
            solidColor = currentColor.toColorInt()
        ),
        title = getString(R.string.shadow_color),
        supportGradient = false,
    ) { selectedColor ->
        createBannerCommonViewModel.updateShadowColor(String.format("#%08X", selectedColor.solidColor))
    }
}

fun CreateBannerTabTextFragment.onClearFormatClick() {
    createBannerCommonViewModel.clearTextFormat()
}

fun CreateBannerTabTextFragment.onBoldClick() {
    createBannerCommonViewModel.toggleTextBold()
}

fun CreateBannerTabTextFragment.onItalicClick() {
    createBannerCommonViewModel.toggleTextItalic()
}

fun CreateBannerTabTextFragment.onUnderlineClick() {
    createBannerCommonViewModel.toggleTextUnderline()
}

fun CreateBannerTabTextFragment.createFontUIModelSelectorList(fonts: List<FontUIModel>): List<FontUIModelSelector> {
    val result = fonts.map { font ->
        FontUIModelSelector(
            fontUIModel = font,
            isSelected = font.name == createBannerCommonViewModel.selectedFontUiModel.value.name
        )
    }.toMutableList()

    result.add(0, FontUIModelSelector(FontUIModel.createDefaultFont(), isSelected = false))

    if (result.all { !it.isSelected } && result.isNotEmpty()) {
        result[0] = result[0].copy(isSelected = true)
    }

    return result.toList()
}

fun CreateBannerTabTextFragment.onFontSelectionClick() {
    when(commonViewModel.getFontUiState.value) {
        is UiState.Success -> {
            //Timber.d("onFontSelectionClick: Success ${commonViewModel.getFontUiState.value}")
            val fonts = (commonViewModel.getFontUiState.value as UiState.Success).data
            createBannerCommonViewModel.setShowLoadingText(true)
            Timber.d("onFontSelectionClick: fonts $fonts")
            fonts.preloadPreviewFont(
                context = requireContext(),
                coroutineScope = viewLifecycleOwner.lifecycleScope,
                timeOut = 6000L,
                onSuccess = {
                    createBannerCommonViewModel.setShowLoadingText(false)
                    val bottomSheet = FontSelectionBottomSheet.newInstance(createFontUIModelSelectorList(fonts))
                    bottomSheet.setOnFontSelectedListener { fontItem ->
                        Timber.d("onFontSelected: $fontItem")
                        fontItem?.let {
                            createBannerCommonViewModel.setSelectedFontUiModel(it)
                        }
                    }
                    bottomSheet.show(parentFragmentManager)
                },
                onFailure = {
                    createBannerCommonViewModel.setShowLoadingText(false)
                    Timber.d("onFontSelectionClick: Preload font failed")
                }
            )
        }

        is UiState.Error -> {
            //Timber.d("onFontSelectionClick: Error ${commonViewModel.getFontUiState.value}")
            displayToast(getString(R.string.something_error))
        }

        is UiState.Loading -> {
            // Handle loading state
            //Timber.d("onFontSelectionClick: Loading ${commonViewModel.getFontUiState.value}")
            displayToast(getString(R.string.loading))
        }

        is UiState.None -> {
            //Timber.d("onFontSelectionClick: None ${commonViewModel.getFontUiState.value}")
            // Handle none state
        }
    }
}