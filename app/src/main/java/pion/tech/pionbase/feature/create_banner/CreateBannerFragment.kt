package pion.tech.pionbase.feature.create_banner

import android.view.View
import dagger.hilt.android.AndroidEntryPoint
import pion.tech.pionbase.base.BaseFragment
import pion.tech.pionbase.custom_view.applyTextStyleDataWithAnimationAndLifecycle
import pion.tech.pionbase.data.model.create_banner.CreateBannerScreenTab
import pion.tech.pionbase.databinding.FragmentCreateBannerBinding
import pion.tech.pionbase.util.animRotation
import pion.tech.pionbase.util.collectFlowOnView
import pion.tech.pionbase.util.handleUiState
import pion.tech.pionbase.util.setPreventDoubleClick
import timber.log.Timber

@AndroidEntryPoint
class CreateBannerFragment : BaseFragment<FragmentCreateBannerBinding, CreateBannerViewModel>(
    FragmentCreateBannerBinding::inflate,
    CreateBannerViewModel::class.java,
) {
    override fun init(view: View) {
        setUpUISelectedTab(CreateBannerScreenTab.TEXT)
        initView()
        displayTextChangedEvent()
        setUpTabClickEvents()
    }

    private fun setUpTabClickEvents() {
        binding.tvTabText.setPreventDoubleClick {
            setUpUISelectedTab(CreateBannerScreenTab.TEXT)
        }

        binding.tvBackground.setPreventDoubleClick {
            setUpUISelectedTab(CreateBannerScreenTab.BACKGROUND)
        }

        binding.tvTabEffect.setPreventDoubleClick {
            setUpUISelectedTab(CreateBannerScreenTab.EFFECT)
        }

        binding.tvTabMusic.setPreventDoubleClick {
            setUpUISelectedTab(CreateBannerScreenTab.MUSIC)
        }
    }

    override fun subscribeObserver(view: View) {
        commonViewModel.getCategoryUiState.collectFlowOnView(viewLifecycleOwner) {
            it.handleUiState(
                onLoading = {
                    Timber.d("getCategoryUiState: onLoading")
                    showHideLoading(true)
                },
                onSuccess = { listAppCategory ->
                    Timber.d("getCategoryUiState: onSuccess")
                    showHideLoading(false)
                    val fontsCategoryId =
                        listAppCategory.firstOrNull { item -> item.name == "Fonts" }?.id
                    if (fontsCategoryId != null) {
                        Timber.d("getCategoryUiState: getFont")
                        commonViewModel.getFont(fontsCategoryId)
                    }
                },
                onError = {
                    Timber.d("getCategoryUiState: onError")
                    showHideLoading(false)
                },
            )
        }

        createBannerCommonViewModel.textStyleData.collectFlowOnView(viewLifecycleOwner) { textStyleData ->
            binding.ledView.applyTextStyleDataWithAnimationAndLifecycle(textStyleData, viewLifecycleOwner, false)
        }

        createBannerCommonViewModel.showLoadingText.collectFlowOnView(viewLifecycleOwner) { isShow ->
            binding.layoutLoading.root.visibility = if (isShow) View.VISIBLE else View.GONE
            if (isShow) {
                binding.layoutLoading.loadingIcon.animRotation()
            }
        }
    }

    companion object {
        const val TAG = "CreateBannerFragment"
        const val DEFAULT_DISPLAY_TEXT = "LED Banner View 🚀✨"
    }
}