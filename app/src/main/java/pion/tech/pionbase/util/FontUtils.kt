package pion.tech.pionbase.util

import android.content.Context
import android.graphics.Typeface
import android.widget.TextView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.net.HttpURLConnection
import java.net.URL

object FontUtils {
    /**
     * Load font from URL and apply to TextView
     */
    suspend fun loadAndApplyFont(
        context: Context,
        fontUrl: String?,
        textView: TextView,
        onSuccess: () -> Unit = {},
        onFailure: () -> Unit = {},
    ) {
        withContext(Dispatchers.IO) {
            if (fontUrl.isNullOrEmpty()) {
                onFailure()
                return@withContext
            }

            try {
                val typeface = loadFontFromUrl(context, fontUrl)
                withContext(Dispatchers.Main) {
                    textView.typeface = typeface
                    onSuccess()
                }
            } catch (e: Exception) {
                e.printStackTrace()
                withContext(Dispatchers.Main) {
                    onFailure()
                }
            }
        }
    }

    /**
     * Load font from URL and cache it locally
     */
    internal suspend fun loadFontFromUrl(
        context: Context,
        fontUrl: String,
    ): Typeface =
        withContext(Dispatchers.IO) {
            val fileName = fontUrl.hashCode().toString() + ".ttf"
            val cacheDir = File(context.filesDir, "fonts_theme")
            if (!cacheDir.exists()) {
                cacheDir.mkdirs()
            }

            val fontFile = File(cacheDir, fileName)

            // Check if font is already cached
            if (!fontFile.exists()) {
                try {
                    // Download font
                    val url = URL(fontUrl)
                    val connection = url.openConnection()
                    connection.connectTimeout = 10000 // 10 seconds timeout
                    connection.readTimeout = 30000 // 30 seconds timeout
                    connection.connect()

                    // Get response code for HTTP connections
                    val responseCode =
                        if (connection is HttpURLConnection) {
                            connection.responseCode
                        } else {
                            200
                        }

                    // Check if response is successful
                    if (responseCode !in 200..299) {
                        throw Exception("HTTP error: $responseCode")
                    }

                    val inputStream = connection.getInputStream()
                    val outputStream = FileOutputStream(fontFile)

                    inputStream.use { input ->
                        outputStream.use { output ->
                            val buffer = ByteArray(8192)
                            var bytesRead: Int
                            while (input.read(buffer).also { bytesRead = it } != -1) {
                                output.write(buffer, 0, bytesRead)
                            }
                            output.flush()
                        }
                    }

                    // Verify file was downloaded successfully
                    if (!fontFile.exists() || fontFile.length() == 0L) {
                        fontFile.delete()
                        throw Exception("Font download failed: file is empty or not created")
                    }
                } catch (e: Exception) {
                    // Clean up partial download
                    if (fontFile.exists()) {
                        fontFile.delete()
                    }
                    throw Exception("Failed to download font from URL: $fontUrl", e)
                }
            }

            // Create typeface from cached file with error handling
            try {
                val typeface =
                    Typeface.createFromFile(fontFile)
                        ?: throw Exception("Typeface creation returned null")
                // Verify typeface was created successfully
                typeface
            } catch (e: Exception) {
                // If font file is corrupted, delete it and throw error
                if (fontFile.exists()) {
                    fontFile.delete()
                }
                throw Exception("Failed to create typeface from file: ${fontFile.absolutePath}", e)
            }
        }
}
