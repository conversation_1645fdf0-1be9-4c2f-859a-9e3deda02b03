<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_bottom_sheet_rounded"
        android:backgroundTint="@color/black_0c111d"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingTop="@dimen/_16dp"
        android:paddingBottom="@dimen/_24dp">

        <!-- Header with title and close button -->
        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_40dp"
            android:fontFamily="@font/font_600"
            android:gravity="center"
            android:text="@string/select_a_font"
            android:textColor="@color/white"
            android:textSize="@dimen/_18sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/btnClose"
            android:layout_width="@dimen/_24dp"
            android:layout_height="@dimen/_24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/close"
            android:src="@drawable/baseline_close_24"
            app:layout_constraintBottom_toBottomOf="@id/tvTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvTitle"
            app:tint="@color/white" />

        <!-- Font list -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvFonts"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_24dp"
            android:maxHeight="400dp"
            android:scrollbars="vertical"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvTitle"
            app:spanCount="2"
            tools:listitem="@layout/item_font_selection" />

        <!-- Apply button -->
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnApply"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_48dp"
            android:layout_marginTop="@dimen/_24dp"
            android:background="@drawable/bg_button_gradient"
            android:fontFamily="@font/font_600"
            android:text="@string/apply"
            android:textColor="@color/white"
            android:textSize="@dimen/_16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/rvFonts" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>