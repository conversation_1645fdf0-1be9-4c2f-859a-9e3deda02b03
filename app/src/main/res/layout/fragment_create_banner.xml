<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_0c111d">

        <!-- Top bar with back & export button -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <FrameLayout
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/ivBack"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/_16dp"
                    android:contentDescription="@null"
                    app:tint="@color/white"
                    android:padding="@dimen/_6dp"
                    android:src="@drawable/ic_back" />

                <TextView
                    android:id="@+id/tvExport"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical|end"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:padding="@dimen/_6dp"
                    android:background="@drawable/bg_button_export"
                    android:fontFamily="@font/font_500"
                    android:text="@string/export"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_12sp" />
            </FrameLayout>

            <pion.tech.pionbase.custom_view.LEDView
                android:id="@+id/ledView"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintDimensionRatio="H,16:9"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/toolbar"
                app:led_text="LED Banner View 🚀✨"
                app:led_textSize="@dimen/_50sp"
                app:led_textColor="@color/white"
                app:led_isBold="false"
                app:led_isItalic="false"
                app:led_isUnderline="false"
                app:led_fontFamily="Roboto"
                app:led_outlineWidth="0dp"
                app:led_outlineColor="@color/red"
                app:led_shadowRadius="0dp"
                app:led_shadowColor="@color/gray_85888e"
                app:led_animationSpeed="20000"
                app:led_dotRadius="@dimen/_2dp"
                app:led_dotSpacing="@dimen/_1dp"
                app:led_backgroundColor="@color/black" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:paddingVertical="@dimen/_8dp"
                app:layout_constraintTop_toBottomOf="@id/ledView">

                <EditText
                    android:id="@+id/edt_display_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10dp"
                    android:layout_marginEnd="@dimen/_10dp"
                    android:background="@drawable/bg_radius_8_black_1241ef"
                    android:drawablePadding="@dimen/_10dp"
                    android:hint="@string/enter_you_text"
                    android:inputType="text"
                    android:padding="@dimen/_10dp"
                    android:textColor="@color/white"
                    android:textColorHint="@color/_61646c"
                    android:textSize="@dimen/_14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/btnPlayBanner"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/btnPlayBanner"
                    android:layout_width="@dimen/_40dp"
                    android:layout_height="@dimen/_40dp"
                    android:layout_marginEnd="@dimen/_10dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:src="@drawable/button_play_banner_screen" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Tab Section -->
            <HorizontalScrollView
                android:id="@+id/horizontalScrollView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/layoutText"
                android:fillViewport="true">

                <!-- Tab Container -->
                <LinearLayout
                    android:id="@+id/ll_tab_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingBottom="@dimen/_12dp">

                    <TextView
                        android:id="@+id/tv_tab_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:scrollHorizontally="true"
                        android:textAlignment="center"
                        android:background="@drawable/bg_tab_selected"
                        android:clickable="true"
                        android:focusable="true"
                        android:singleLine="true"
                        android:ellipsize="end"
                        android:padding="@dimen/_8dp"
                        android:text="@string/text"
                        android:textColor="@color/blue_1b71f4"
                        android:textSize="@dimen/_14dp" />

                    <TextView
                        android:id="@+id/tv_background"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:singleLine="true"
                        android:scrollHorizontally="true"
                        android:textAlignment="center"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:fontFamily="sans-serif-medium"
                        android:padding="@dimen/_8dp"
                        android:text="@string/background"
                        android:textColor="@color/text_secondary"
                        android:textSize="@dimen/_14dp" />

                    <TextView
                        android:id="@+id/tv_tab_effect"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:singleLine="true"
                        android:ellipsize="end"
                        android:textAlignment="center"
                        android:scrollHorizontally="true"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:padding="@dimen/_8dp"
                        android:text="@string/effects"
                        android:textColor="@color/text_secondary"
                        android:textSize="@dimen/_14dp" />

                    <TextView
                        android:id="@+id/tv_tab_music"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:singleLine="true"
                        android:ellipsize="end"
                        android:scrollHorizontally="true"
                        android:textAlignment="center"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:padding="@dimen/_8dp"
                        android:text="@string/music"
                        android:textColor="@color/text_secondary"
                        android:textSize="@dimen/_14dp" />

                </LinearLayout>
            </HorizontalScrollView>

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintTop_toBottomOf="@id/horizontalScrollView"
                app:layout_constraintBottom_toBottomOf="parent"
                android:fillViewport="true">

                <FrameLayout
                    android:id="@+id/fragmentContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </ScrollView>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <include
            android:id="@+id/layoutLoading"
            layout="@layout/layout_loading_text"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
