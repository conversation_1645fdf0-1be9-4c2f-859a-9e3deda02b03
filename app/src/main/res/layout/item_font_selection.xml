<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/_50dp"
        android:layout_margin="@dimen/_4dp">

        <!-- Selection overlay -->
        <ImageView
            android:id="@+id/ivSelectionOverlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/ic_background_font_unselected"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!-- Font name with preview -->
        <TextView
            android:id="@+id/tvFontName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/white_cecfd2"
            android:textSize="@dimen/_16sp"
            android:singleLine="true"
            android:ellipsize="marquee"
            android:marqueeRepeatLimit="marquee_forever"
            android:focusable="true"
            android:focusableInTouchMode="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Roboto" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>

