<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutLoading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible"
        tools:background="#4D000000">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_radius_8_black_1241ef"
            android:backgroundTint="#333741"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:padding="@dimen/_25dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintWidth_percent="0.7"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/loadingIcon"
                android:layout_width="@dimen/_50dp"
                android:layout_height="@dimen/_50dp"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/ic_loading_view" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_500"
                android:layout_marginTop="@dimen/_10dp"
                android:minWidth="@dimen/_100dp"
                android:textSize="@dimen/_18sp"
                android:text="@string/loading_font"
                android:textColor="@color/white" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>